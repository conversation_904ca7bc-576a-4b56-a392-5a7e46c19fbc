<template>
  <div>
    <div class="">
      <a-select
        v-model:value="selectRoute"
        show-search
        placeholder="输入内容搜索"
        style="width: 200px"
        :options="menuOptions"
        :filterOption="filterOption"
        @select="handleSelect"
      ></a-select>
    </div>
    <div class="flex items-center gap-4 report-content flex-wrap">
      <template v-for="(item, index) in btnList" :key="index">
        <div class="rounded-lg btn-box text-white">
          {{ item.label }}
        </div>
      </template>
    </div>
    <div class="iframe">
      <iframe
        src="https://bi.ssnj.com/index.html#/aj/bbB5dUYF"
        style="width: 100%; height: 100%; min-height: 800px"
        frameborder="0"
        @load="handleIframeLoad"
      >
      </iframe>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { getRoutersV1 } from "@/api/menu";
import { useRoute, useRouter } from "vue-router";
const router = useRouter();
const keyword = ref();
const menuOptions = ref([]);
const selectRoute = ref();
const btnList = ref([
  {
    label: "销售合同",
    route: "saleContract",
  },
  {
    label: "销售合同",
    route: "saleContract",
  },
  {
    label: "销售合同",
    route: "saleContract",
  },
  {
    label: "销售合同",
    route: "saleContract",
  },
  {
    label: "销售合同",
    route: "saleContract",
  },
  {
    label: "销售合同",
    route: "saleContract",
  },
  {
    label: "销售合同",
    route: "saleContract",
  },
]);

const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
const getAllRoutes = async () => {
  let res = await getRoutersV1({ keyWord: keyword.value || "" });
  menuOptions.value = res.data.map((item) => {
    return {
      label: item.menuName,
      value: item.path,
    };
  });
};
const handleSelect = (e) => {
  //
  console.log("[ e ] >", e);
  router.push({
    name: e,
  });
};

onMounted(() => {
  console.log(window);
  window.CHATBOT_CONFIG = {
    endpoint: "https://webchat-bot-fim-jktrdusadm.cn-hangzhou.fcapp.run/chat", // 可以替换为 https://{your-fc-http-trigger-domain}/chat
    displayByDefault: true, // 默认不显示 AI 助手对话框
    title: "福瑞助手", // 自定义 AI 助手标题
    draggable: true, // 是否开启拖拽
    aiChatOptions: {
      // 自定义取值参考：https://docs.nlkit.com/nlux/reference/ui/ai-chat#conversation-options

      conversationOptions: {
        // 自定义取值参考：https://docs.nlkit.com/nlux/reference/ui/ai-chat#conversation-options
        layout: "bubbles",
        conversationStarters: [
          { prompt: "预测一下磷酸铁锂未来半年的价格趋势？" },
          { prompt: "福瑞集团2月份采购了多少种物料？" },
          { prompt: "减速器市场行情?" },
        ],
      },
      displayOptions: {
        // 自定义取值参考：https://docs.nlkit.com/nlux/reference/ui/ai-chat#display-options
        height: 600,
        // width: 400,
      },
      personaOptions: {
        // 自定义取值参考：https://docs.nlkit.com/nlux/reference/ui/ai-chat#chat-personas
        assistant: {
          name: "你好，我是你的 AI 助手",
          // AI 助手的图标
          avatar:
            "https://img.alicdn.com/imgextra/i2/O1CN01Pda9nq1YDV0mnZ31H_!!6000000003025-54-tps-120-120.apng",
          tagline: "您可以尝试点击下方的快捷入口开启体验！",
        },
      },
      messageOptions: {
        // 自定义取值参考：https://docs.nlkit.com/nlux/reference/ui/ai-chat#message-options
        waitTimeBeforeStreamCompletion: "never",
      },
    },
    dataProcessor: {
      /**
       * 在向后端大模型应用发起请求前改写 Prompt。
       * 比如可以用于总结网页场景，在发送前将网页内容包含在内，同时避免在前端显示这些内容。
       * @param {string} prompt - 用户输入的 Prompt
       * @param {string}  - 改写后的 Prompt
       */
      rewritePrompt(prompt) {
        return prompt;
      },
    },
  };
  getAllRoutes();
});
</script>

<style lang="less" scoped>
.iframe {
  width: 100%;
  height: calc(100vh-180px); /* 根据实际布局调整 */
  margin-top: 30px;
}
.report-content {
  padding-right: 300px;
}
.btn-box {
  width: 240px;
  height: 100px;
  background: #1890ff;
  text-align: center;
  line-height: 100px;
  cursor: pointer;
  user-select: none;
}
</style>

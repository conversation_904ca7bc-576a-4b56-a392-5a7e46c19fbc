<template>
  <div class="mb-4 text-right">
    <search :searchData="searchData" @search="refresh">
      <mw-button
        v-permission="'market:contract:add'"
        :font="'iconfont icon-xianxing-121'"
        @click="onOpen()"
        :title="'新增'"
      ></mw-button>
    </search>
  </div>
  <mw-table
    :scroll="{ x: 'max-content' }"
    :columns="columns"
    :data-source="data"
    :loading="loading"
    :rowKey="(record) => record.id"
    hasPage
    @change="onTableChange"
    :pageConfig="paginationProps"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key == 'contractName'">
        <div>{{ record.contractName }}</div>

        <div class="secondary-color">编号：{{ record.contractNo }}</div>
        <div class="secondary-color">
          类型：

          <dictionary
            :statusOptions="AllContractTypeList"
            :value="record.contractType"
          />
        </div>
      </template>
      <template v-if="column.key == 'status'">
        <dictionary
          :statusOptions="statusList"
          :value="record.status"
          isBackgroundColor
        />
      </template>
      <template v-if="column.key == 'tenantId'">
        <div v-if="record.tenantId == 28">徐州</div>
        <div v-if="record.tenantId == 37">蚌埠</div>
      </template>
      <template v-if="column.key == 'examineStatus'">
        <div
          v-if="
            record.status == '0' || record.status == '1' || record.status == '8'
          "
        >
          审批通过
        </div>
        <a-tag color="blue" v-if="record.status == '7'">待审批</a-tag>
        <div v-if="record.status == '9'">审批不通过</div>
        <div v-if="record.status == '10'">审批撤销</div>
        <div v-if="record.status == '3'">待补充</div>
      </template>

      <template v-if="column.key == 'productMarketClassification'">
        <dictionary
          :statusOptions="AllProductTypes"
          :value="record.productMarketClassification"
        />
      </template>

      <template v-if="column.key == 'operation'">
        <mw-button
          @click="onOpenDetail(record.id, 'det', record)"
          :title="'详情'"
          class="mr-2"
        ></mw-button>
        <mw-button
          @click="exportOrg(record)"
          class="mr-2"
          v-permission="'market:contract:exportExcel'"
          :title="'导出'"
        ></mw-button>

        <a-popconfirm
          v-if="record.status == 0"
          title="请确认是否作废"
          ok-text="确定"
          cancel-text="取消"
          @confirm="onCancellation(record.id)"
          @cancel="cancel"
        >
          <mw-button
            :title="'作废'"
            class="mr-2"
            v-if="record.status == 0"
            :disabled="record.tenantId != userStore.user.tenantId"
            danger
          ></mw-button>
        </a-popconfirm>

        <mw-button
          @click="onOpen(record.id, 'edit')"
          v-if="record.status == 9 || record.status == 10 || record.status == 3"
          :disabled="record.tenantId != userStore.user.tenantId"
          >编辑</mw-button
        >
      </template>
    </template>
  </mw-table>
  <!-- 新增 -->
  <add-contract-review-drawer
    ref="refAddContractReviewDrawer"
    v-model:visible="visibleAddContractReviewDrawer"
    @finish="getList"
    v-model:id="contractReviewId"
    :type="contractReviewType"
    :valItem="valItem"
  />
</template>
<script setup>
import Search from "@/components/search/index.vue";
import { usePagenation } from "@/common/setup";
import { ref, onBeforeMount, reactive, toRaw, getCurrentInstance } from "vue";
import addContractReviewDrawer from "./addContractReviewDrawer.vue";
// import detailContractReviewDrawer from "./detailContractReviewDrawer.vue";
import {
  getContractReviewList,
  getCancellation,
  contractExportExcel,
} from "@/api/sales/index.js";
import { statusList, useCountryList } from "@/common/constant.js";
import { exportExecl } from "@/utils/util.js";
import { contractTypeList, businessTypes } from "@/common/constant.js";
import {
  productMarketClassificationList,
  productTypes,
} from "@/common/constant.js";
import { useUserStore } from "@/stores/user.js";
const userStore = useUserStore();
const AllContractTypeList = ref([...contractTypeList, ...businessTypes]);
const AllProductTypes = ref([
  ...productMarketClassificationList,
  ...productTypes,
]);
const { proxy } = getCurrentInstance();
const visibleAddContractReviewDrawer = ref(false);
const contractReviewType = ref("");
const contractReviewId = ref("");
const data = ref([]);
const loading = ref(false);
const refAddContractReviewDrawer = ref(null);
const exportLoading = ref(false);
const valItem = ref();
const columns = ref([
  {
    title: "合同名称",
    dataIndex: "contractName",
    key: "contractName",
  },
  {
    title: "合同状态",
    dataIndex: "status",
    key: "status",
  },
  {
    title: "所属企业",
    dataIndex: "tenantId",
    key: "tenantId",
  },

  // {
  //   title: "合同编号",
  //   dataIndex: "contractNo",
  //   key: "contractNo",
  // },
  // {
  //   title: "合同类型",
  //   dataIndex: "contractType",
  //   key: "contractType",
  // },
  {
    title: "产品类别",
    dataIndex: "productMarketClassification",
    key: "productMarketClassification",
  },
  {
    title: "业务所属人名称",
    dataIndex: "bizBelongUserName",
    key: "bizBelongUserName",
  },
  // {
  //   title: "评审日期",
  //   dataIndex: "reviewTime",
  //   key: "reviewTime",
  //   width: "180px",
  // },
  // {
  //   title: "交货日期",
  //   dataIndex: "deliveryTime",
  //   key: "deliveryTime",
  //   width: "180px",
  // },
  {
    title: "产品总数",
    dataIndex: "productQuantity",
    key: "productQuantity",
  },
  {
    title: "已下单数",
    dataIndex: "beAlreadyQuantity",
    key: "beAlreadyQuantity",
  },
  {
    title: "已发货数",
    dataIndex: "quantityShipped",
    key: "quantityShipped",
  },
  {
    title: "操作",
    dataIndex: "operation",
    key: "operation",
    fixed: "right",
  },
]);
const searchData = ref({
  searchButtons: [],
  operationButtons: [],
  fields: {
    status: {
      name: "产品分类",
      type: "a-select",
      options: [],
      placeholder: "选择分类",
      width: "120px",

      value: "",
      allowClear: true,
    },
    useCountry: {
      name: "使用国家",
      type: "a-select",
      options: [
        {
          label: "全部",
          value: "",
        },
        ...useCountryList,
      ],
      placeholder: "选择国家",
      width: "120px",
      value: "",
      allowClear: true,
    },
    keyword: {
      type: "a-input-search",
      placeholder: "请输入合同编号/合同名称/客户名称",
      width: "240px",
      allowClear: true,
    },
  },
});
searchData.value.fields.status.options = [
  {
    label: "全部分类",
    value: "",
  },
  ...statusList,
];

const getList = async () => {
  loading.value = true;
  //分页信息
  let searchParam = {};
  //搜索信息
  for (const key in searchData.value.fields) {
    searchParam[key] = searchData.value.fields[key].value;
  }
  let result = await getContractReviewList({ ...pageParam.value }, searchParam);
  // let result = await getContractReviewList(pageParam.value, param);
  data.value = result.data;
  paginationProps.value.total = result.total;
  loading.value = false;
};
onBeforeMount(async () => {
  await getList();
});
const { paginationProps, onTableChange, refresh, pageParam } =
  usePagenation(getList);
// 新2222增
const onOpen = (valId, val) => {
  contractReviewType.value = val;
  contractReviewId.value = valId;
  visibleAddContractReviewDrawer.value = true;
};
// 详情
const onOpenDetail = (valId, val, record) => {
  contractReviewType.value = val;
  contractReviewId.value = valId;
  visibleAddContractReviewDrawer.value = true;
  valItem.value = record;
};

async function onConfirm(record) {
  getList();
}

const onCancellation = async (val) => {
  let res = await getCancellation({ id: val });
  if (res.code == 200) {
    proxy.$message.success("成功作废");
    getList();
  } else {
    proxy.$message.success(res.msg);
  }
};
// 导出
const exportOrg = async (record) => {
  // console.log(record);
  exportLoading.value = true;

  let result = await contractExportExcel({ contractId: record.id });
  const fileName = "合同评审.xlsx";
  exportExecl(fileName, result);
  exportLoading.value = false;
};
</script>

<style lang="less" scoped>
:deep(.ant-table-tbody > tr > td) {
  padding: 10px 12px;
}
</style>

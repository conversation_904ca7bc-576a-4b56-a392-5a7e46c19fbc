<template>
  <mw-drawer
    :visible="visible"
    @close="onClose()"
    width="80%"
    :customTitle="
      props.openType == 'add'
        ? '新增销售订单'
        : props.openType == 'detail'
        ? '详情销售订单'
        : '编辑详情订单'
    "
  >
    <template #header>
      <mw-button
        @click="formSubmit"
        :loading="submitLoading"
        v-if="props.openType == 'add' || props.openType == 'edit'"
        >确定</mw-button
      >

      <!-- {{ props.contractReviewStatus }} -->
    </template>
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="horizontal"
      :colon="false"
      labelAlign="left"
    >
      <a-row>
        <a-col :span="12">
          <a-form-item
            :label-col="{ span: 7 }"
            label="订单类型"
            required
            name="type"
          >
            <a-select
              placeholder="请选择订单类型"
              :options="orderTypeList"
              v-model:value="formData.type"
              optionFilterProp="label"
              @change="handleChangeOrderType"
              show-search
              :disabled="props.openType == 'detail' || props.openType == 'edit'"
            >
            </a-select
          ></a-form-item>
        </a-col>
      </a-row>
      <a-row> </a-row>
      <a-row>
        <a-col
          :span="12"
          v-if="
            formData.type == 'PRODUCTION_ORDER' ||
            formData.type == 'OUT_SOURCE_ORDER'
          "
        >
          <!-- <a-form-item
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
            label="关联-销售合同"
            name="contractId"
            required
            v-if="
              (props.contractReviewStatus == 9 && props.id) ||
              (props.contractReviewStatus == 2 && props.id) ||
              (props.contractReviewStatus == 10 && props.id)
            "
          >
            <a-input v-model:value="formData.contractName" disabled />
            {{ formData.contractName }}</a-form-item
          > -->

          <a-form-item
            :label-col="{ span: 7 }"
            label="关联-订单合同"
            name="contractId"
            required
          >
            <a-select
              placeholder="请选择关联-订单合同"
              :options="postNoContractReviewListOptions"
              v-model:value="formData.contractId"
              optionFilterProp="label"
              @change="handleChange"
              show-search
              :disabled="
                props.openType == 'detail'
                  ? true
                  : props.openType == 'edit'
                  ? true
                  : false
              "
            >
            </a-select>
          </a-form-item>
        </a-col>

        <a-col
          :span="12"
          v-if="
            formData.type == 'PRODUCTION_ORDER' ||
            formData.type == 'OUT_SOURCE_ORDER'
          "
        >
          <a-form-item label="合同编号" :label-col="{ span: 7, offset: 2 }">
            <a-input
              v-model:value="formData.contractList.contractNo"
              disabled
            />
          </a-form-item>
        </a-col>
        <!-- <a-col :span="12">
          <a-form-item
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
            label="订单编号"
          >
            <a-input
              v-model:value="formData.contractList.orderNo"
              placeholder="订单编号"
              disabled
            />
          </a-form-item>
        </a-col> -->
        <a-col
          :span="12"
          v-if="
            formData.type == 'PRODUCTION_ORDER' ||
            formData.type == 'OUT_SOURCE_ORDER'
          "
        >
          <a-form-item label="业务-归属人" :label-col="{ span: 7 }">
            <a-input
              v-model:value="formData.contractList.bizBelongUserName"
              disabled
            />
          </a-form-item>
        </a-col>
        <a-col
          :span="12"
          v-if="
            formData.type == 'PRODUCTION_ORDER' ||
            formData.type == 'OUT_SOURCE_ORDER'
          "
        >
          <a-form-item label="客户代码" :label-col="{ span: 7, offset: 2 }">
            <a-input
              v-model:value="formData.contractList.customerId"
              disabled
            />
          </a-form-item>
        </a-col>
        <a-col
          :span="12"
          v-if="
            formData.type == 'PRODUCTION_ORDER' ||
            formData.type == 'OUT_SOURCE_ORDER'
          "
        >
          <a-form-item label="产品市场分类" :label-col="{ span: 7 }">
            <a-input
              v-model:value="formData.contractList.productMarketClassification"
              disabled
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item
            label="交货日期"
            :label-col="
              !formData.type ||
              (formData.type !== 'PRODUCTION_ORDER' &&
                formData.type !== 'OUT_SOURCE_ORDER')
                ? { span: 7 }
                : { span: 7, offset: 2 }
            "
            required
            name="deliveryTime"
          >
            <a-date-picker
              v-if="!planNo"
              v-model:value="formData.deliveryTime"
              style="width: 100%"
              valueFormat="YYYY-MM-DD"
              :disabled="props.openType == 'detail' || props.openType == 'det'"
            />
            <span v-else>{{ formData.deliveryTime }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-row>
            <a-col :span="12">
              <a-form-item
                label="订单名称"
                name="orderName"
                required
                :label-col="{ span: 7 }"
              >
                <a-input
                  v-model:value="formData.orderName"
                  placeholder="请输入订单名称"
                  :disabled="props.openType == 'detail' ? true : false"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row>
            <a-col :span="12">
              <a-form-item
                label="是否自动生成编号"
                name="autoGenerationCodeFlag"
                :label-col="{ span: 7 }"
                required
              >
                <radioGroup
                  v-model:value="formData.autoGenerationCodeFlag"
                  :options="autoGenerationCodeFlagList"
                  @change="changeAutoGenerationCodeFlag"
                  :disabled="
                    props.openType == 'detail' || props.openType == 'edit'
                  "
                >
                </radioGroup>
              </a-form-item>
            </a-col>
            <!--   -->
            <a-col :span="12">
              <a-form-item
                label="订单命令编号"
                name="orderNo"
                :label-col="{ span: 7, offset: 2 }"
                v-if="formData.autoGenerationCodeFlag == 0"
              >
                <a-input
                  v-model:value="formData.orderNo"
                  placeholder="订单命令编号"
                  :disabled="
                    props.openType == 'detail' || props.openType == 'det'
                  "
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-col>

        <a-col :span="12">
          <a-form-item
            label="订单备注"
            :label-col="
              !formData.type ||
              (formData.type !== 'PRODUCTION_ORDER' &&
                formData.type !== 'OUT_SOURCE_ORDER')
                ? { span: 7 }
                : { span: 7 }
            "
          >
            <a-textarea
              v-model:value="formData.remark"
              placeholder="请输入订单备注"
              allow-clear
              :disabled="props.openType == 'detail' || props.openType == 'det'"
            />
          </a-form-item>
        </a-col>

        <!-- <a-col :span="12">
          <a-form-item
            label="使用国家"
            name="useCountry"
            required
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
          >
            <a-radio-group
              v-model:value="formData.useCountry"
              :options="useCountryList"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="国外国家"
            v-if="formData.useCountry == 1"
            name="foreignCountry"
            required
            :label-col="{ span: 7, offset: 2 }"
            :wrapper-col="{ span: 17, offset: 1 }"
          >
            <a-input v-model:value="formData.foreignCountry" />
          </a-form-item>
        </a-col> -->
        <a-col :span="24">
          <mw-button
            class="mb-2"
            @click="addOrderDetailList"
            v-if="
              formData.type !== 'PRODUCTION_ORDER' &&
              formData.type !== 'OUT_SOURCE_ORDER' &&
              props.openType !== 'detail'
            "
          >
            新增
          </mw-button>

          <mw-table
            :scroll="{ x: 'max-content' }"
            :data-source="dataSourceData"
            :columns="visibleColumns"
            :pagination="{ pageSize: 5 }"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'materialId'">
                <!-- <a-select
                  option=""
                  class="w-full"
                  v-if="formData.type !== 'PRODUCTION_ORDER'"
                ></a-select> -->
                <a-select
                  class="w-full"
                  v-if="
                    formData.type !== 'PRODUCTION_ORDER' &&
                    formData.type !== 'OUT_SOURCE_ORDER' &&
                    props.openType !== 'detail'
                  "
                  placeholder="请选择产品"
                  :options="listMaterialData"
                  v-model:value="record.materialId"
                  optionFilterProp="label"
                  @change="
                    (val, option) => handleProductChange(val, option, record)
                  "
                  @search="onSearch"
                  show-search
                >
                </a-select>
                <a
                  v-else
                  @click="onJump('product', record)"
                  style="color: #1890ff"
                  >{{ record.materialNo }}/ {{ record.materialName }}/{{
                    record.specification
                  }}
                </a>
              </template>

              <template v-if="column.dataIndex === 'quantity'">
                <a-input-number
                  v-if="props.openType !== 'detail'"
                  v-model:value="record.quantity"
                  :disabled="
                    props.openType == 'detail' || props.openType == 'det'
                  "
                  :stringMode="true"
                ></a-input-number>
              </template>
              <!-- <template v-if="column.dataIndex === 'totalQuantity'">
                <a-input-number
                  v-model:value="record.totalQuantity"
                  :disabled="
                    props.openType == 'detail' || props.openType == 'det'
                  "
                ></a-input-number>
              </template> -->
              <template v-if="column.dataIndex === 'remark'">
                <a-textarea
                  v-if="props.openType !== 'detail'"
                  v-model:value="record.remark"
                  :disabled="
                    props.openType == 'detail' || props.openType == 'det'
                  "
                ></a-textarea>
              </template>
              <template v-if="column.dataIndex === 'bomName'">
                <div v-if="record.isHomemade == 1">
                  <div
                    v-if="
                      (record.materialId && props.openType == 'edit') ||
                      (record.materialId && props.openType == 'add')
                    "
                  >
                    <a
                      @click="modifyBom(record, index)"
                      style="color: #1890ff"
                      >{{ record.bomName || "请选择BOM" }}</a
                    >
                  </div>
                  <span v-else>{{ record.bomName }}</span>
                </div>
                <div v-else>--</div>
              </template>
              <template v-if="column.dataIndex === 'use'">
                <a-input
                  v-model:value="record.use"
                  :disabled="
                    props.openType == 'detail' || props.openType == 'det'
                  "
                ></a-input>
              </template>
              <template v-if="column.dataIndex === 'isNeedDebugging'">
                <a-select
                  v-if="props.openType !== 'detail'"
                  class="w-full"
                  v-model:value="record.isNeedDebugging"
                  placeholder="请选择物料单位"
                  :default-active-first-option="false"
                  :not-found-content="null"
                  :options="whetherDebugging"
                  :disabled="
                    props.openType == 'detail' || props.openType == 'det'
                  "
                ></a-select>
              </template>
              <template
                v-if="
                  column.dataIndex === 'operation' &&
                  props.openType !== 'detail'
                "
              >
                <a-popconfirm
                  title="确定是否删除"
                  @confirm="onDelete(record, index)"
                >
                  <mw-button danger v-if="dataSourceData.length > 1">
                    删除
                  </mw-button>
                </a-popconfirm>
              </template>
              <!-- 需求更改暂时注释
                <template v-if="column.dataIndex === 'file'" >
                <div v-if="record.file?.fileVisitUrl">
                  <i
                    class="iconfont icon-jichu-lianjie text-xs align-middle mr-1"
                    style="color: #959ec3"
                  ></i>
                  <a
                    :href="record.file?.fileVisitUrl"
                    :title="record.file?.fileVisitUrl"
                    target="_blank"
                    class="underline"
                    style="color: #959ec3"
                    >{{ record.file?.fileName }}
                  </a>
                </div>
              </template> -->
            </template>
          </mw-table>
        </a-col>
        <a-col :span="12">
          <a-form-item label="单体标识">
            <a-checkbox-group
              v-model:value="formData.orderConfig.monomerIdentification"
              name="checkboxgroup"
              :options="orderConfigList.monomerIdentification"
              :disabled="props.openType == 'detail' || props.openType == 'det'"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="包装要求">
            <a-checkbox-group
              v-model:value="formData.orderConfig.packingRequirement"
              name="checkboxgroup"
              :options="orderConfigList.packingRequirement"
              :disabled="props.openType == 'detail' || props.openType == 'det'"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="随货文件">
            <a-checkbox-group
              v-model:value="formData.orderConfig.accompanyingDocument"
              name="checkboxgroup"
              :options="orderConfigList.accompanyingDocument"
              :disabled="props.openType == 'detail' || props.openType == 'det'"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="1.机箱标识 (电源)">
            <a-checkbox-group
              v-model:value="formData.orderConfig.chassisIdentification"
              name="checkboxgroup"
              :options="orderConfigList.chassisIdentification"
              :disabled="props.openType == 'detail' || props.openType == 'det'"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="2.说明书 (电源)">
            <a-checkbox-group
              v-model:value="formData.orderConfig.specification"
              name="checkboxgroup"
              :options="orderConfigList.specification"
              :disabled="props.openType == 'detail' || props.openType == 'det'"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="3.铭牌 (电源)">
            <a-checkbox-group
              v-model:value="formData.orderConfig.dogtag"
              name="checkboxgroup"
              :options="orderConfigList.dogtag"
              :disabled="props.openType == 'detail' || props.openType == 'det'"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="4.充电机铭牌 (电源)">
            <a-checkbox-group
              v-model:value="formData.orderConfig.chargerNameplate"
              name="checkboxgroup"
              :options="orderConfigList.chargerNameplate"
              :disabled="props.openType == 'detail' || props.openType == 'det'"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="5.充电机显示屏 (电源)">
            <a-checkbox-group
              v-model:value="formData.orderConfig.chargerDisplay"
              name="checkboxgroup"
              :options="orderConfigList.chargerDisplay"
              :disabled="props.openType == 'detail' || props.openType == 'det'"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="6.充电机说明书 (电源)">
            <a-checkbox-group
              v-model:value="formData.orderConfig.chargerManual"
              name="checkboxgroup"
              :options="orderConfigList.chargerManual"
              :disabled="props.openType == 'detail' || props.openType == 'det'"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="特殊要求">
            <a-textarea
              style="width: 100%"
              v-model:value="formData.orderConfig.specialRequirements"
              placeholder="请输入特殊要求"
              allow-clear
              :disabled="props.openType == 'detail' || props.openType == 'det'"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <modifyBomDrawer
      v-model:visible="modifyBomDrawerVisible"
      :bomDetailData="bomDetailData"
      @bomID-child="bomIDChild"
      :bomIndex="bomIndex"
    ></modifyBomDrawer>
  </mw-drawer>
</template>
<script setup>
import {
  ref,
  reactive,
  getCurrentInstance,
  defineProps,
  defineEmits,
  watch,
  computed,
} from "vue";
import {
  orderConfigList,
  productMarketClassificationList,
  orderTypeList,
  whetherDebugging,
  productTypes,
} from "@/common/constant.js";
const AllProductTypes = ref([
  ...productMarketClassificationList,
  ...productTypes,
]);
import { useRoute, useRouter } from "vue-router";
import {
  postSaleOrderAdd,
  getContractReviewDetail,
  postSaleOrderUpdate,
  postNoContractReviewList,
  getSaleOrderDetail,
  dictDataType,
} from "@/api/sales/index.js";
// import { postCustomerPage, productList } from "@/api/proSchedul/scheduling.js"; //客户信息
import { AllList, AllPage } from "@/api/basicData/material.js";
import modifyBomDrawer from "./modifyBomDrawer.vue";
import { useUserStore } from "@/stores/user.js";
import { debounce } from "lodash";
import { getBomDetailById } from "@/api/basicData/bom.js";
import { autoGenerationCodeFlagList } from "@/common/constant.js";
// import FormUpload from "@/components/form-upload.vue";
// import { bomDetail } from "../../../api/plan";
// const productListData = ref([]); //需求注释暂时保留
const postNoContractReviewListOptions = ref([]);
const emit = defineEmits(["update:visible", "finish"]);
const route = useRoute();
const router = useRouter();
const store = useUserStore();
const dictTypeData = ref([]);
const modifyBomDrawerVisible = ref(false);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true,
  },
  contractReviewStatus: {
    type: String,
    required: true,
  },
  openType: {
    type: String,
    required: true,
  },
});
const { proxy } = getCurrentInstance();
const formRef = ref();
const customerPageList = ref([]);
const submitLoading = ref(false);
const bomDetailData = ref();
const listMaterialData = ref();
const formData = reactive({
  contractId: void 0,
  deliveryTime: void 0,
  orderName: void 0,
  orderNo: void 0,
  contractList: {
    contractNo: void 0,
    customerId: void 0,
    bizBelongUserName: void 0,
    productMarketClassification: void 0,
    totalContractAmount: void 0,
    // reviewTime: "",
  },
  remark: void 0,
  useCountry: void 0,
  foreignCountry: void 0,
  orderConfig: {
    monomerIdentification: [],
    packingRequirement: [],
    accompanyingDocument: [],
    chassisIdentification: [],
    specification: [],
    dogtag: [],
    chargerNameplate: [],
    chargerDisplay: [],
    chargerManual: [],
    specialRequirements: void 0,
  },
  autoGenerationCodeFlag: 0,
});
const bomIndex = ref();
const dataSourceData = ref([]);
const columnsRelationProduct = ref([
  {
    title: "产品",
    dataIndex: "materialId",
    width: "300px",
  },
  {
    //暂时注释
    title: "自制",
    dataIndex: "isHomemade",
    key: "isHomemade",
    customRender: ({ record }) => {
      return record.isHomemade == 1
        ? "是"
        : record.isHomemade == 0
        ? "否"
        : "-";
    },
  },
  {
    title: "关联BOM",
    dataIndex: "bomName",
  },
  // {
  //   title: "单价",
  //   dataIndex: "unitPrice",
  // },
  {
    title: "产品总数",
    dataIndex: "productQuantity",
    // dataIndex: "totalQuantity",
  },
  {
    //暂时注释
    title: "可下单数量",
    dataIndex: "sbQuantity",
    width: 120,
  },
  {
    title: "本次下单数",
    dataIndex: "quantity",
  },
  // {
  //   title: "本次下单数",
  //   dataIndex: "quantity",
  //   width: 120,
  // },
  // {
  //   title: "金额",
  //   dataIndex: "amount",
  // },

  {
    title: "备注",
    dataIndex: "remark",
  },
  {
    title: "是否需要调试",
    dataIndex: "isNeedDebugging",
  },
  {
    title: "操作",
    dataIndex: "operation",
  },
]);
const visibleColumns = computed(() => {
  if (
    formData.type == "PRODUCTION_ORDER" ||
    formData.type == "OUT_SOURCE_ORDER"
  ) {
    console.log("111111");
    if (props.openType == "detail") {
      return columnsRelationProduct.value.filter(
        (column) => column.dataIndex !== "sbQuantity"
      ); // 如果不满足条件，返回除了列1和列2以外的其他列
    } else {
      return columnsRelationProduct.value; // 如果满足条件，返回所有列
    }
  } else {
    return columnsRelationProduct.value.filter(
      (column) =>
        column.dataIndex !== "isNeedDebugging" &&
        column.dataIndex !== "productQuantity" &&
        column.dataIndex !== "sbQuantity"
    ); // 如果不满足条件，返回除了列1和列2以外的其他列
  }
});
const rules = reactive({
  // materialIdOrProductId: [
  //   {
  //     required: true,
  //     message: "请选择" + props.name,
  //     trigger: "blur",
  //   },
  // ],
});

// 删除
const onDelete = (item, ind) => {
  const index = dataSourceData.value.findIndex(
    (i) => i.materialId === item.materialId
  );
  dataSourceData.value.splice(index, 1);
};
const bomIDChild = (e, ind) => {
  console.log(e, ind, "子组件传来的参数");
  // console.log(bomDetailData, "bomDetailData");
  // bomItemId
  dataSourceData.value.forEach((item, index) => {
    console.log(item, index);
    console.log(index, bomIndex.value);
    if (index == bomIndex.value) {
      item.bomName = e.bomName;
      item.bomId = e.id;
      item.bomNo = e.bomNo;
    }
  });
};
// 新增明细列表
const addOrderDetailList = () => {
  dataSourceData.value.push({});
};
// 订单类型的change事件
const handleChangeOrderType = () => {
  Object.assign(formData, {
    contractId: void 0,
    orderName: void 0,
    deliveryTime: void 0,
    remark: void 0,
  });

  dataSourceData.value = [];

  Object.assign(formData.contractList, {
    contractNo: void 0,
    customerId: void 0,
    bizBelongUserName: void 0,
    productMarketClassification: void 0,
  });

  // 根据订单类型设置订单命令编号默认值
  updateOrderNoByType();
};

// 根据订单类型更新订单命令编号
const updateOrderNoByType = () => {
  // 关联合同的订单类型：外协订单、生产订单
  const contractRelatedTypes = ["OUT_SOURCE_ORDER", "PRODUCTION_ORDER"];
  // 不关联合同的订单类型：内部订单、售后订单、研发订单
  const nonContractTypes = [
    "INTERNAL_ORDER",
    "AFTER_SALE_ORDER",
    "DEVELOPMENT_ORDER",
  ];

  if (contractRelatedTypes.includes(formData.type)) {
    // 关联合同的订单类型：如果有合同编号，则继承合同编号
    if (formData.contractList?.contractNo) {
      formData.orderNo = formData.contractList.contractNo;
    } else {
      formData.orderNo = void 0;
    }
  } else if (nonContractTypes.includes(formData.type)) {
    // 不关联合同的订单类型：默认为空
    formData.orderNo = void 0;
  }
};

// 修改bom
const modifyBom = (val, index) => {
  console.log("bom列表", val);
  console.log(bomDetailData, "bomDetailData");
  // formData.relationContractId=''
  modifyBomDrawerVisible.value = true;
  bomDetailData.value = val;
  bomIndex.value = index;
};
function onClose() {
  emit("update:visible", false);
  props.id = void 0;
  formRef.value.resetFields();
  formData.type = void 0;
  formData.contractId = void 0;
  formData.orderName = void 0;
  formData.orderNo = void 0;
  formData.contractList = {};
  formData.remark = void 0;
  formData.useCountry = void 0;
  formData.foreignCountry = void 0;
  formData.orderConfig = {};
  dataSourceData.value = [];
}
// // 客户
// const postCustomerPageList =  async()=>{
//       let res=await postCustomerPage()
//       customerPageList.value=res.data
// }
// // 产品名称productList
// const postproductList =async () =>{
//   let res= await productList()
//        productListData.value = res.data

// }
// 关联-销售合同
const postNoContractReview = async () => {
  let param = {
    statusList: props.id ? [0, 4, 1] : [0, 4],
  };
  const { data } = await postNoContractReviewList(param);
  console.log(data, "销售合同");
  postNoContractReviewListOptions.value =
    data?.map((item) => {
      return {
        label: `合同 ${item.contractName}（${item.contractNo}）`,
        value: String(item.id),
      };
    }) || [];
};
const handleChange = (e) => {
  getOrderDetail(e);
};
const getOrderDetail = async (val) => {
  let res = await getContractReviewDetail({ id: val });

  formData.contractList.contractNo = res.data.contractNo;
  formData.contractList.customerId = res.data.customerNo;
  formData.contractList.bizBelongUserName = res.data.bizBelongUserName;

  const item = AllProductTypes.value.find(
    (item) => item.value === res.data.productMarketClassification
  );
  console.log("[ item ] >", item);
  formData.contractList.productMarketClassification = item?.label;
  formData.remark = res.data.remark;
  formData.contractList.totalContractAmount = res.data.totalContractAmount;
  dataSourceData.value = res?.data?.contractMaterialRelationList?.map(
    (item) => {
      console.log(item.isHomemade, "item");
      return {
        ...item,
        remark: item?.use ? (item.use == "null" ? "" : item.use) : "",
        productQuantity: item.totalQuantity,
        sbQuantity: item.quantity,
        isHomemade: item.isHomemade,
      };
    }
  );
  formData.currencyType = res.data.currencyType;

  // 选择合同后，更新订单命令编号
  updateOrderNoByType();
};

// 编辑详情
// contractReviewId
const getDetail = async (val) => {
  let res = await getSaleOrderDetail({ id: val });
  // if (res.data.type == "PRODUCTION_ORDER") {
  //   getOrderDetail(res.data.contractId);
  // } else {
  //   //
  //   dataSourceData.value = res.data.marketOrderMaterialRelationList;

  //   formData.remark = res.data.remark;
  // }
  formData.contractList.contractNo = res.data?.contractNo;
  formData.contractList.customerId = res.data?.customerNo;
  const item = AllProductTypes.value.find(
    (item) => item.value === res.data?.productMarketClassification
  );
  formData.contractList.productMarketClassification = item?.label;
  formData.contractList.bizBelongUserName = res.data?.bizBelongUserName;
  formData.remark = res.data.remark;
  dataSourceData.value = res.data.marketOrderMaterialRelationList.map(
    (item) => {
      item.sbQuantity = item.quantity;
      item.quantity = item.totalQuantity;
      return item;
    }
  );
  formData.orderNo = res.data.orderNo;
  formData.orderName = res.data.orderName;
  console.log(res.data.contractId, "res.data.contractId");
  formData.contractId = String(res.data.contractId);
  formData.type = res.data.type;
  formData.useCountry = res.data.useCountry;

  formData.deliveryTime = res.data.deliveryTime;
  formData.orderConfig.monomerIdentification =
    res.data.orderConfigVO.monomerIdentification;
  formData.orderConfig.packingRequirement =
    res.data.orderConfigVO.packingRequirement;
  formData.orderConfig.accompanyingDocument =
    res.data.orderConfigVO.accompanyingDocument;
  formData.orderConfig.chassisIdentification =
    res.data.orderConfigVO.chassisIdentification;

  formData.orderConfig.chargerManual = res.data.orderConfigVO.chargerManual;
  formData.orderConfig.chargerNameplate =
    res.data.orderConfigVO.chargerNameplate;
  formData.orderConfig.chargerDisplay = res.data.orderConfigVO.chargerDisplay;
  formData.orderConfig.specialRequirements =
    res.data.orderConfigVO.specialRequirements;
  formData.orderConfig.specification = res.data.orderConfigVO.specification;
  formData.orderConfig.dogtag = res.data.orderConfigVO.dogtag;
};

// 产品明细添加
//   const handleAdd = () => {
//   const newData = {
//       id: Date.now(),
//       productId:'',
//       unitPrice:0.00,
//       quantity:"",
//       amount:0.00,
//       isNeedDebugging:'',
//       productUse:'',
//   };
//   formData.relationProductList.push(newData);
// };
// 产品删除
// const onDelete =(val) =>{

//   formData.relationProductList.forEach((element,index) => {
//     if(props.id){
//       if(element.productId==val.productId){
//         formData.relationProductList.splice(index,1)
//       }
//     }else{
//       if(element.id==val.id){
//         formData.relationProductList.splice(index,1)
//       }
//     }

//   });
// }
// 确认新增
const formSubmit = async () => {
  formRef.value.validate().then(async () => {
    try {
      if (formData.useCountry !== 1) {
        delete formData.foreignCountry;
      }
      const hasOneSatisfied = dataSourceData.value.every((item) => {
        item.quantity > item.sbQuantity;
      });
      if (hasOneSatisfied) {
        proxy.$message.error("下单数量不能大于可下单数量");
        return;
      }
      let param = {
        ...formData,
        relationMaterialList: dataSourceData.value,
        // autoGenerationCodeFlag:
        //   formData.type == "PRODUCTION_ORDER" ||
        //   formData.type == "OUT_SOURCE_ORDER"
        //     ? 0
        //     : 1,
      };

      submitLoading.value = true;
      let res;
      if (props.id && props.openType == "edit") {
        param.id = props.id;
        res = await postSaleOrderUpdate(param);
      } else {
        res = await postSaleOrderAdd(param);
      }
      if (res.code == 200) {
        proxy.$message.success("操作成功");
        onClose();
        emit("finish");
      }
      submitLoading.value = false;
    } catch (error) {
      submitLoading.value = false;
    }
  });
};
const onJump = (val, record = {}) => {
  if (val == "product") {
    store.setParamsBomNo(record.materialNo);
    // const url = import.meta.env.VITE_DOMAIN + "/basicData/newmaterial";
    router.push({
      name: "Newmaterial",
    });
  } else {
    const url = import.meta.env.VITE_DOMAIN + "/basicData/bom";
    router.push({
      name: "Bom",
    });
  }
};
// 字典查询
const getDictDataType = async () => {
  let dictType = "market_currency_type";
  let res = await dictDataType(dictType);
  dictTypeData.value = res.data;
};
//销售订单查询物料
const getListMaterial = async (keyword) => {
  let prams = {
    // isHomemade: 1,
    ignoreCancel: true,
    ignoreStop: true,
    ignoreStatus: false,
    keyword,
  };
  let res = await AllPage(prams);
  listMaterialData.value =
    res.data.map((item) => {
      item.label = `${item.materialNo}/${item.materialName}/${item.specification}`;
      item.value = item.id;
      return item;
    }) || [];

  // res.data.map((item) => {
  //     item. label=`${item.materialNo}/${item.materialName}/${item.specification}`,
  //     item.  value= item.id,

  //   }) || [];
  console.log(res, "res");
};

const debouncedSearch = debounce(async (e) => {
  console.log("[ e ] >", e);
  getListMaterial(e);
  //
}, 300);
const onSearch = async (e) => {
  debouncedSearch(e);
  //
};
const handleProductChange = async (e, item, value) => {
  console.log("[ item ] >", item.bomNo);
  if (item.bomId) {
    let res = await getBomDetailById({
      bomNo: item.bomId,
    });
    value.bomNo = res.data.bomNo;
    value.bomName = res.data.bomName;
  }
  value.productQuantity = item.availableStock;
  value.isHomemade = item.isHomemade;
  value.quantity = item.quantity;
  value.remark = item.remark;
  value.isNeedDebugging = item.isNeedDebugging;
};
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      await getListMaterial();

      if (props.id) {
        // getOrderDetail(props.id);
        await getDetail(props.id);
      }
      await getDictDataType(); //字典查询
      postNoContractReview();
      // postCustomerPageList()
      // postproductList()
    }
  }
);
const changeAutoGenerationCodeFlag = () => {
  formData.orderNo = void 0;
  if (formData.autoGenerationCodeFlag == 0) {
    if (formData.contractList?.contractNo) {
      formData.orderNo = formData.contractList.contractNo;
    } else {
      formData.orderNo = void 0;
    }
  }
};
</script>
<style lang="less" scoped></style>
